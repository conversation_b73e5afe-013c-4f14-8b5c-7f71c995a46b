// API Keys configuration
export const API_KEYS = {
  OPENAI_API_KEY: import.meta.env.WXT_OPENAI_API_KEY || '',
  GEMINI_API_KEY: import.meta.env.WXT_GEMINI_API_KEY || '',
  SUPABASE_URL: import.meta.env.WXT_SUPABASE_URL || '',
  SUPABASE_ANON_KEY: import.meta.env.WXT_SUPABASE_ANON_KEY || ''
};

// Validate API keys
export function validateApiKeys(): { openai: boolean; gemini: boolean } {
  return {
    openai: Boolean(API_KEYS.OPENAI_API_KEY),
    gemini: Boolean(API_KEYS.GEMINI_API_KEY)
  };
} 