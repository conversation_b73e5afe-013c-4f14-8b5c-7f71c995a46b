import { useState, useEffect, useRef } from 'react';
import { Button, CircularProgress, Typography, Box, Snackbar, Collapse, ButtonGroup, ClickAwayListener, Grow, Paper, Popper, MenuItem, MenuList, Divider } from '@mui/material';
import { handlePerfectPrompt, type OptimizedPrompt } from '@/utils/perfectPrompt';
import CloseIcon from '@mui/icons-material/Close';
import PromptSaveModal, { PromptData } from './PromptSaveModal';
import { getPromptLibrary, addPromptLibrary } from '@/utils/promptLibrary';

export default function PromptPanel({ initialPrompt, setPromptPanelValue }: { initialPrompt: string; setPromptPanelValue: (value: string) => void }) {
  const [prompt, setPrompt] = useState(initialPrompt);
  const [optimizing, setOptimizing] = useState(false);
  const [optimizedPrompts, setOptimizedPrompts] = useState<OptimizedPrompt[]>([]);
  const [selectedLLM, setSelectedLLM] = useState<'ChatGPT' | 'Claude' | 'Gemini'>('ChatGPT');
  const [recentPrompts, setRecentPrompts] = useState<any[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({ open: false, message: '', severity: 'success' });
  const [open, setOpen] = useState(false);
  const [copiedPromptId, setCopiedPromptId] = useState<string | null>(null);
  const anchorRef = useRef<HTMLDivElement>(null);
  const [saveModalOpen, setSaveModalOpen] = useState(false);
  const [allFolders, setAllFolders] = useState<string[]>([]);
  
  // Separate state for AI Suggestions dropdown
  const [suggestionsOpen, setSuggestionsOpen] = useState(false);
  const suggestionsAnchorRef = useRef<HTMLDivElement>(null);
  
  // Separate state for each suggestion item's dropdown
  const [suggestionDropdowns, setSuggestionDropdowns] = useState<{ [key: string]: boolean }>({});
  const suggestionAnchorRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Restore last prompt and optimized results on mount
  useEffect(() => {
    chrome.storage.local.get(['lastPromptPanel'], (result) => {
      console.log('Restored from storage:', result.lastPromptPanel);
      if (result.lastPromptPanel) {
        if (!prompt) setPrompt(result.lastPromptPanel.prompt || '');
        if (!optimizedPrompts.length) setOptimizedPrompts(result.lastPromptPanel.optimizedPrompts || []);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Persist prompt and optimized results on change
  useEffect(() => {
    // Only save if there is a prompt or optimized results
    if (prompt || (optimizedPrompts && optimizedPrompts.length > 0)) {
      chrome.storage.local.set({
        lastPromptPanel: {
          prompt,
          optimizedPrompts
        }
      });
    }
  }, [prompt, optimizedPrompts]);

  useEffect(() => {
    const loadRecent = async () => {
      const library = await getPromptLibrary();
      if (Array.isArray(library)) {
        // Sort by updatedAt, take top 5
        const sorted = [...library].sort((a, b) => b.updatedAt - a.updatedAt);
        setRecentPrompts(sorted.slice(0, 5));
      } else {
        setRecentPrompts([]);
      }
    };
    loadRecent();
    const handleChange = () => {
      loadRecent();
    };
    // @ts-ignore
    chrome.storage.onChanged.addListener(handleChange);
    return () => {
      // @ts-ignore
      chrome.storage.onChanged.removeListener(handleChange);
    };
  }, []);

  useEffect(() => {
    const loadFolders = async () => {
      const library = await getPromptLibrary();
      if (Array.isArray(library)) {
        const folders = Array.from(new Set(library.map((item: any) => item.folder).filter(Boolean)));
        setAllFolders(folders);
      }
    };
    loadFolders();
  }, []);

  const handleOptimize = async () => {
    if (!prompt.trim()) {
      setSnackbar({ open: true, message: 'Prompt is empty.', severity: 'error' });
      return;
    }
    setOptimizing(true);
    setOptimizedPrompts([]);
    try {
      const result = await handlePerfectPrompt(prompt, 'openai');
      // Remove all double quotes from each optimized prompt's text
      const cleanedResult = result.map(opt => ({
        ...opt,
        text: opt.text.replace(/"/g, '')
      }));
      setOptimizedPrompts(cleanedResult);
      setSnackbar({ open: true, message: 'Optimized prompt(s) generated!', severity: 'success' });
    } catch (err: any) {
      setSnackbar({ open: true, message: err.message || 'Failed to optimize prompt.', severity: 'error' });
    } finally {
      setOptimizing(false);
    }
  };

  const handleCopy = (text: string, promptId: string) => {
    navigator.clipboard.writeText(text);
    setCopiedPromptId(promptId);
    setTimeout(() => setCopiedPromptId(null), 3000);
  };

  const handleSend = async (optimizedPrompt?: string) => {
    if (!optimizedPrompt?.trim() && !prompt.trim()) {
      setSnackbar({ open: true, message: 'Prompt is empty.', severity: 'error' });
      return;
    }

    let url = '';
    let selector = '';

    switch (selectedLLM) {
      case 'ChatGPT':
        url = 'https://chat.openai.com/';
        selector = 'textarea[data-id="root"], textarea[placeholder*="Message"], textarea[placeholder*="Send a message"]';
        break;
      case 'Gemini':
        url = 'https://gemini.google.com/app';
        selector = 'textarea[placeholder*="Message"], textarea[aria-label*="Message"], .ql-editor, [contenteditable="true"]';
        break;
      case 'Claude':
        url = 'https://claude.ai/';
        selector = 'textarea[placeholder*="Message"], textarea[aria-label*="Message"], .ql-editor, [contenteditable="true"]';
        break;
      default:
        setSnackbar({ open: true, message: 'Invalid LLM selection.', severity: 'error' });
        return;
    }

    setSnackbar({ open: true, message: `Opening ${selectedLLM}...`, severity: 'success' });

    // Send message to background to handle tab, injection, handshake, and fillPrompt
    chrome.runtime.sendMessage({
      action: 'openAndFillPrompt',
      url,
      prompt: optimizedPrompt || prompt,
      selector
    }, (response) => {
      if (response && response.success) {
        setSnackbar({ open: true, message: `Prompt filled in ${selectedLLM}!`, severity: 'success' });
      } else {
        setSnackbar({ open: true, message: response && response.error ? response.error : 'Failed to fill prompt.', severity: 'error' });
      }
    });
  };

  const handleOpenSaveModal = (optimizedText?: string) => {
    // If optimizedText is provided, use it; otherwise use current prompt
    const textToSave = optimizedText || prompt;
    setPrompt(textToSave);
    setPromptPanelValue(textToSave);
    setSaveModalOpen(true);
  };
  const handleCloseSaveModal = () => {
    setSaveModalOpen(false);
  };
  const handleSavePromptModal = async (data: PromptData) => {
    await addPromptLibrary(data);
    setSaveModalOpen(false);
    setSnackbar({ open: true, message: 'Prompt saved to library!', severity: 'success' });
  };

  const handleUseOptimized = (text: string) => {
    setPrompt(text);
    setPromptPanelValue(text);
    setSnackbar({ open: true, message: 'Optimized prompt copied!', severity: 'success' });
  };

  // Split button handlers
  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };
  const handleClose = (event: Event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target as HTMLElement)) {
      return;
    }
    setOpen(false);
  };
  const handleLLMSelect = (llm: 'ChatGPT' | 'Claude' | 'Gemini') => {
    setSelectedLLM(llm);
    setOpen(false);
  };
  
  // Suggestions dropdown handlers
  const handleSuggestionsToggle = () => {
    setSuggestionsOpen((prevOpen) => !prevOpen);
  };
  const handleSuggestionsClose = (event: Event) => {
    if (suggestionsAnchorRef.current && suggestionsAnchorRef.current.contains(event.target as HTMLElement)) {
      return;
    }
    setSuggestionsOpen(false);
  };
  const handleSuggestionsLLMSelect = (llm: 'ChatGPT' | 'Claude' | 'Gemini') => {
    setSelectedLLM(llm);
    setSuggestionsOpen(false);
  };
  
  // Individual suggestion dropdown handlers
  const handleSuggestionDropdownToggle = (suggestionId: string) => {
    setSuggestionDropdowns(prev => ({
      ...prev,
      [suggestionId]: !prev[suggestionId]
    }));
  };
  
  const handleSuggestionDropdownClose = (suggestionId: string, event: Event) => {
    if (suggestionAnchorRefs.current[suggestionId] && suggestionAnchorRefs.current[suggestionId]?.contains(event.target as HTMLElement)) {
      return;
    }
    setSuggestionDropdowns(prev => ({
      ...prev,
      [suggestionId]: false
    }));
  };
  
  const handleSuggestionLLMSelect = (suggestionId: string, llm: 'ChatGPT' | 'Claude' | 'Gemini') => {
    setSelectedLLM(llm);
    setSuggestionDropdowns(prev => ({
      ...prev,
      [suggestionId]: false
    }));
  };
  
  const handleSendOptimized = (text: string) => {
    setPrompt(text);
    setPromptPanelValue(text);
    handleSend(text);
  };

  const handleSendOptimizedWithLLM = (text: string, llm: 'ChatGPT' | 'Claude' | 'Gemini') => {
    setPrompt(text);
    setPromptPanelValue(text);
    setSelectedLLM(llm);
    handleSend(text);
  };

  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column', 
      height: '100%', 
      background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
      overflow: 'hidden'
    }}>
      {/* 🧠 Prompt Input Section */}
      <Box sx={{ 
        p: 3, 
        background: '#ffffff', 
        borderBottom: '1px solid #e2e8f0',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <Typography 
          variant="h6" 
          sx={{ 
            mb: 2, 
            display: 'flex', 
            alignItems: 'center', 
            gap: 1,
            fontSize: '1.1rem',
            fontWeight: 600,
            color: '#1e293b'
          }}
        >
          🧠 Prompt Input
        </Typography>
        <Box sx={{ 
          border: '2px solid #e2e8f0', 
          borderRadius: 3, 
          p: 1, 
          background: '#fafbfc',
          transition: 'all 0.2s ease-in-out',
          '&:focus-within': {
            borderColor: '#3b82f6',
            boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)'
          }
        }}>
          <textarea
            value={prompt}
            onChange={e => setPrompt(e.target.value)}
            rows={Math.min(6, Math.max(3, prompt.split('\n').length))}
            style={{ 
              width: '100%', 
              resize: 'vertical', 
              minHeight: 100, 
              maxHeight: 200, 
              fontSize: 15, 
              border: 'none', 
              outline: 'none', 
              background: 'transparent', 
              fontFamily: 'inherit',
              lineHeight: 1.5,
              color: '#374151',
              padding: 0
            }}
            placeholder="Type your prompt here..."
            maxLength={2000}
          />
        </Box>
        
        {/* Optimize Button moved here */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          mt: 2 
        }}>
          <Button
            variant="contained"
            size="small"
            onClick={handleOptimize}
            disabled={optimizing}
            sx={{ 
              minWidth: 140, 
              fontWeight: 600, 
              fontSize: 12, 
              borderRadius: 2.5, 
              background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
              boxShadow: '0 4px 6px -1px rgba(139, 92, 246, 0.3)',
              px: 3, 
              py: 1,
              '&:hover': { 
                background: 'linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%)',
                boxShadow: '0 6px 8px -1px rgba(139, 92, 246, 0.4)',
                transform: 'translateY(-1px)'
              },
              '&:disabled': {
                background: '#9ca3af',
                boxShadow: 'none',
                transform: 'none'
              }
            }}
          >
            {optimizing ? <CircularProgress size={20} color="inherit" /> : '✨ Optimize'}
          </Button>
        </Box>
      </Box>

      {/* ✨ Suggestions Section */}
      {optimizedPrompts.length > 0 && (
        <Box sx={{ 
          p: 3, 
          background: '#ffffff', 
          borderBottom: '1px solid #e2e8f0'
        }}>
          <Typography 
            variant="h6" 
            sx={{ 
              mb: 2, 
              display: 'flex', 
              alignItems: 'center', 
              gap: 1,
              fontSize: '1.1rem',
              fontWeight: 600,
              color: '#1e293b'
            }}
          >
            ✨ AI Suggestions
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
            {optimizedPrompts.map(opt => (
              <Box 
                key={opt.id} 
                sx={{ 
                  display: 'flex', 
                  flexDirection: 'column', 
                  gap: 1.5, 
                  p: 2, 
                  border: '1px solid #e2e8f0', 
                  borderRadius: 2, 
                  background: '#f8fafc',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    borderColor: '#3b82f6',
                    background: '#f0f9ff',
                    transform: 'translateY(-1px)',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }
                }}
              >
                <Box sx={{ flex: 1, minWidth: 0 }}>
                  <Typography 
                    fontWeight={600} 
                    fontSize={14} 
                    color="#2563eb"
                    sx={{ mb: 0.5 }}
                  >
                    {opt.style}
                  </Typography>
                  <Typography 
                    fontSize={14} 
                    sx={{ 
                      lineHeight: 1.5,
                      color: '#374151',
                      wordBreak: 'break-word'
                    }}
                  >
                    {opt.text}
                  </Typography>
                </Box>
                <Box sx={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: 1,
                  flexShrink: 0,
                  mt: 2
                }}>
                  <Button 
                    size="small" 
                    variant="outlined" 
                    onClick={() => {handleCopy(opt.text, opt.id); handleUseOptimized(opt.text);}}
                    sx={{ 
                      minWidth: 'auto',
                      px: 1.5,
                      py: 0.5,
                      fontSize: '0.75rem',
                      borderRadius: 1.5,
                      borderColor: '#d1d5db',
                      color: '#6b7280',
                      '&:hover': {
                        borderColor: '#9ca3af',
                        color: '#374151'
                      }
                    }}
                  >
                    {copiedPromptId === opt.id ? "✅ Copied!" : "📋 Copy"}
                  </Button>
                  <Button 
                    size="small" 
                    variant="outlined" 
                    onClick={() => handleOpenSaveModal(opt.text)}
                    sx={{ 
                      minWidth: 'auto',
                      px: 1.5,
                      py: 0.5,
                      fontSize: '0.75rem',
                      borderRadius: 1.5,
                      borderColor: '#0891b2',
                      color: '#0891b2',
                      '&:hover': {
                        borderColor: '#0e7490',
                        color: '#0e7490',
                        background: '#ecfeff'
                      }
                    }}
                  >
                    💾 Save
                  </Button>
                  <Box sx={{ position: 'relative' }}>
                    <ButtonGroup
                      variant="contained"
                      size="small"
                      ref={(el) => { suggestionAnchorRefs.current[opt.id] = el; }}
                      sx={{ 
                        borderRadius: 1.5, 
                        boxShadow: '0 2px 4px -1px rgba(16, 185, 129, 0.3)',
                        overflow: 'hidden',
                        '& .MuiButton-root': { 
                          background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                          color: '#ffffff', 
                          fontWeight: 600, 
                          fontSize: '0.75rem',
                          px: 1.5,
                          py: 0.5,
                          borderRadius: 0,
                          border: 'none',
                          '&:hover': { 
                            background: 'linear-gradient(135deg, #059669 0%, #047857 100%)',
                            transform: 'translateY(-1px)'
                          }
                        },
                        '& .MuiButtonGroup-grouped:not(:last-of-type)': { 
                          borderRight: '1px solid rgba(255, 255, 255, 0.2)' 
                        }
                      }}
                    >
                      <Button
                        onClick={() => handleSendOptimizedWithLLM(opt.text, selectedLLM)}
                        sx={{ flex: 1 }}
                      >
                        📤 Send
                      </Button>
                      <Button
                        aria-controls={suggestionDropdowns[opt.id] ? `suggestion-${opt.id}-menu` : undefined}
                        aria-expanded={suggestionDropdowns[opt.id] ? 'true' : undefined}
                        aria-label="select LLM"
                        aria-haspopup="menu"
                        onClick={() => handleSuggestionDropdownToggle(opt.id)}
                        sx={{ 
                          minWidth: 30,
                          px: 0.5
                        }}
                      >
                        ▾
                      </Button>
                    </ButtonGroup>
                    <Popper
                      sx={{ zIndex: 2000 }}
                      open={suggestionDropdowns[opt.id] || false}
                      anchorEl={suggestionAnchorRefs.current[opt.id]}
                      role={undefined}
                      transition
                      disablePortal
                      placement="bottom-end"
                    >
                      {({ TransitionProps, placement }) => (
                        <Grow
                          {...TransitionProps}
                          style={{ transformOrigin: placement === 'bottom' ? 'center top' : 'center bottom' }}
                        >
                          <Paper sx={{ 
                            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                            borderRadius: 2,
                            overflow: 'hidden'
                          }}>
                            <ClickAwayListener onClickAway={(event) => handleSuggestionDropdownClose(opt.id, event)}>
                              <MenuList id={`suggestion-${opt.id}-menu`} autoFocusItem>
                                <MenuItem 
                                  onClick={() => handleSuggestionLLMSelect(opt.id, 'ChatGPT')} 
                                  selected={selectedLLM === 'ChatGPT'}
                                  sx={{ 
                                    fontSize: 12,
                                    py: 1.5,
                                    '&.Mui-selected': {
                                      background: '#f0f9ff',
                                      color: '#2563eb'
                                    }
                                  }}
                                >
                                  ChatGPT
                                </MenuItem>
                                <MenuItem 
                                  onClick={() => handleSuggestionLLMSelect(opt.id, 'Claude')} 
                                  selected={selectedLLM === 'Claude'}
                                  sx={{ 
                                    fontSize: 12,
                                    py: 1.5,
                                    '&.Mui-selected': {
                                      background: '#f0f9ff',
                                      color: '#2563eb'
                                    }
                                  }}
                                >
                                  Claude
                                </MenuItem>
                                <MenuItem 
                                  onClick={() => handleSuggestionLLMSelect(opt.id, 'Gemini')} 
                                  selected={selectedLLM === 'Gemini'}
                                  sx={{ 
                                    fontSize: 12,
                                    py: 1.5,
                                    '&.Mui-selected': {
                                      background: '#f0f9ff',
                                      color: '#2563eb'
                                    }
                                  }}
                                >
                                  Gemini
                                </MenuItem>
                              </MenuList>
                            </ClickAwayListener>
                          </Paper>
                        </Grow>
                      )}
                    </Popper>
                  </Box>
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
      )}

      {/* 🎯 Actions Section */}
      

      {/* 🕘 Recent Prompts Section */}
      <Box sx={{ 
        p: 3, 
        background: '#ffffff',
        flex: 1,
        overflow: 'hidden'
      }}>
        <Button
          variant="text"
          size="small"
          onClick={() => setShowHistory(h => !h)}
          sx={{ 
            p: 0, 
            minWidth: 'auto', 
            textTransform: 'none', 
            fontSize: '1.1rem', 
            color: '#6b7280',
            fontWeight: 600,
            '&:hover': {
              background: 'transparent',
              color: '#374151'
            }
          }}
        >
          🕘 Recent Prompts {showHistory ? '▾' : '▸'}
        </Button>
        <Collapse in={showHistory}>
          <Box sx={{ 
            mt: 2, 
            maxHeight: 200, 
            overflowY: 'auto',
            pr: 1
          }}>
            {recentPrompts.length === 0 ? (
              <Typography 
                color="text.secondary" 
                fontSize={13} 
                sx={{ 
                  fontStyle: 'italic',
                  textAlign: 'center',
                  py: 2,
                  color: '#9ca3af'
                }}
              >
                No recent prompts.
              </Typography>
            ) : (
              recentPrompts.map((item) => (
                <Box key={item.id} sx={{ p: 1, border: '1px solid #e0e0e0', borderRadius: 1, mb: 1 }}>
                  <Typography fontWeight={600} fontSize={14}>{item.title}</Typography>
                  <Typography fontSize={13} color="text.secondary">{item.content}</Typography>
                  <Button size="small" onClick={() => handleSend(item.content)}>Send</Button>
                </Box>
              ))
            )}
          </Box>
        </Collapse>
      </Box>

      <PromptSaveModal
        open={saveModalOpen}
        onClose={handleCloseSaveModal}
        onSave={handleSavePromptModal}
        initialPrompt={{ title: '', content: prompt, tags: [], folder: '', optimize: '' }}
        allFolders={allFolders}
        mode="add"
      />

      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar(s => ({ ...s, open: false }))}
        message={snackbar.message}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        ContentProps={{ 
          style: { 
            background: snackbar.severity === 'error' ? '#ef4444' : '#10b981', 
            color: '#ffffff',
            borderRadius: 8,
            fontWeight: 500
          } 
        }}
        action={
          <Button
            size="small"
            onClick={() => setSnackbar(s => ({ ...s, open: false }))}
            sx={{ color: '#ffffff', minWidth: 'auto' }}
          >
            <CloseIcon fontSize="small" />
          </Button>
        }
      />
    </Box>
  );
} 