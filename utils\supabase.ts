import {
  createClient,
  SupabaseClient,
  Auth<PERSON>rror,
  PostgrestError,
} from "@supabase/supabase-js";

const SUPABASE_URL = import.meta.env.WXT_SUPABASE_URL || "";
const SUPABASE_ANON_KEY = import.meta.env.WXT_SUPABASE_ANON_KEY || "";

// Create Supabase client with Chrome extension compatibility
export const supabase: SupabaseClient = createClient(
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  {
    auth: {
      // Disable automatic token refresh in Chrome extension environment
      autoRefreshToken: false,
      // Use chrome.storage for persistence instead of localStorage
      persistSession: false,
      // Detect session from URL hash (for OAuth redirects)
      detectSessionInUrl: false,
    },
    // Use fetch for HTTP requests (Chrome extension compatible)
    global: {
      fetch: fetch.bind(globalThis),
    },
  }
);

// Legacy interface for backward compatibility
export interface SupabaseResponse<T = any> {
  data: T | null;
  error: AuthError | PostgrestError | Error | null;
}

// Authentication helper functions for Chrome extension environment
export async function initializeAuth(): Promise<void> {
  // Initialize auth state from chrome.storage if available
  if (typeof chrome !== "undefined" && chrome.storage) {
    try {
      const result = await chrome.storage.local.get(["supabase_session"]);
      if (result.supabase_session) {
        await supabase.auth.setSession(result.supabase_session);
      }
    } catch (error) {
      console.warn("Failed to restore Supabase session:", error);
    }
  }
}

// Save session to chrome.storage
export async function saveSession(): Promise<void> {
  if (typeof chrome !== "undefined" && chrome.storage) {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (session) {
        await chrome.storage.local.set({ supabase_session: session });
      }
    } catch (error) {
      console.warn("Failed to save Supabase session:", error);
    }
  }
}

// Clear session from chrome.storage
export async function clearSession(): Promise<void> {
  if (typeof chrome !== "undefined" && chrome.storage) {
    try {
      await chrome.storage.local.remove(["supabase_session"]);
    } catch (error) {
      console.warn("Failed to clear Supabase session:", error);
    }
  }
}

// Create or update user in Supabase Auth using Google user info
export async function createSupabaseUser(
  userInfo: any,
  accessToken: string
): Promise<{ data: any; error: any }> {
  try {
    // For Chrome extensions, we'll create a custom session approach
    // Since we can't use the standard OAuth flow, we'll store user info locally
    // and sync with our custom users table

    console.log("Creating/updating user session for Chrome extension");

    // Store user info in chrome storage as our session
    if (typeof chrome !== "undefined" && chrome.storage) {
      const sessionData = {
        user: {
          id: userInfo.sub,
          email: userInfo.email,
          user_metadata: {
            full_name: userInfo.name,
            avatar_url: userInfo.picture,
            name: userInfo.name,
            picture: userInfo.picture,
            sub: userInfo.sub,
          },
        },
        access_token: accessToken,
        expires_at: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
        token_type: "bearer",
      };

      await chrome.storage.local.set({ supabase_session: sessionData });
      console.log("Session stored in Chrome storage");
    }

    return { data: { user: userInfo }, error: null };
  } catch (error) {
    console.error("Error creating Supabase user:", error);
    return { data: null, error };
  }
}

// Google OAuth sign in with Supabase Auth (for web contexts)
export async function signInWithGoogle(): Promise<{ data: any; error: any }> {
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: "google",
      options: {
        redirectTo: `chrome-extension://${chrome.runtime.id}/`,
        queryParams: {
          access_type: "offline",
          prompt: "consent",
        },
      },
    });

    if (error) {
      console.error("Supabase Google OAuth error:", error);
      return { data: null, error };
    }

    // Save session after successful authentication
    await saveSession();
    return { data, error: null };
  } catch (error) {
    console.error("Google sign-in error:", error);
    return { data: null, error };
  }
}

// Sign out
export async function signOut(): Promise<{ error: any }> {
  try {
    const { error } = await supabase.auth.signOut();
    await clearSession();
    return { error };
  } catch (error) {
    console.error("Sign out error:", error);
    return { error };
  }
}

// Get current user (Chrome extension compatible)
export async function getCurrentUser() {
  try {
    // First try to get from Supabase
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (user) {
      return { user, error };
    }

    // If no Supabase user, check our custom session
    const { session } = await getCurrentSession();
    if (session?.user) {
      return { user: session.user, error: null };
    }

    return { user: null, error };
  } catch (error) {
    console.error("Get current user error:", error);
    return { user: null, error };
  }
}

// Get current session (Chrome extension compatible)
export async function getCurrentSession() {
  try {
    // First try to get from Supabase
    const {
      data: { session },
      error,
    } = await supabase.auth.getSession();

    if (session) {
      return { session, error };
    }

    // If no Supabase session, check Chrome storage for our custom session
    if (typeof chrome !== "undefined" && chrome.storage) {
      try {
        const result = await chrome.storage.local.get(["supabase_session"]);
        if (result.supabase_session) {
          // Check if session is still valid (not expired)
          const sessionData = result.supabase_session;
          const now = Math.floor(Date.now() / 1000);

          if (sessionData.expires_at && now < sessionData.expires_at) {
            return { session: sessionData, error: null };
          } else {
            // Session expired, clear it
            await chrome.storage.local.remove(["supabase_session"]);
            return { session: null, error: new Error("Session expired") };
          }
        }
      } catch (storageError) {
        console.warn(
          "Failed to get session from Chrome storage:",
          storageError
        );
      }
    }

    return { session: null, error };
  } catch (error) {
    console.error("Get current session error:", error);
    return { session: null, error };
  }
}

// Listen for auth state changes and save session
supabase.auth.onAuthStateChange(async (event, session) => {
  console.log("Supabase auth state changed:", event, session?.user?.email);

  if (event === "SIGNED_IN" && session) {
    await saveSession();
  } else if (event === "SIGNED_OUT") {
    await clearSession();
  }
});

// Initialize auth on module load
initializeAuth().catch(console.error);
