import {
  createClient,
  SupabaseClient,
  Auth<PERSON>rror,
  PostgrestError,
} from "@supabase/supabase-js";

const SUPABASE_URL = import.meta.env.WXT_SUPABASE_URL || "";
const SUPABASE_ANON_KEY = import.meta.env.WXT_SUPABASE_ANON_KEY || "";

// Create Supabase client with Chrome extension compatibility
export const supabase: SupabaseClient = createClient(
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  {
    auth: {
      // Disable automatic token refresh in Chrome extension environment
      autoRefreshToken: false,
      // Use chrome.storage for persistence instead of localStorage
      persistSession: false,
      // Detect session from URL hash (for OAuth redirects)
      detectSessionInUrl: false,
    },
    // Use fetch for HTTP requests (Chrome extension compatible)
    global: {
      fetch: fetch.bind(globalThis),
    },
  }
);

// Legacy interface for backward compatibility
export interface SupabaseResponse<T = any> {
  data: T | null;
  error: AuthError | PostgrestError | Error | null;
}

// Authentication helper functions for Chrome extension environment
export async function initializeAuth(): Promise<void> {
  // Initialize auth state from chrome.storage if available
  if (typeof chrome !== "undefined" && chrome.storage) {
    try {
      const result = await chrome.storage.local.get(["supabase_session"]);
      if (result.supabase_session) {
        await supabase.auth.setSession(result.supabase_session);
      }
    } catch (error) {
      console.warn("Failed to restore Supabase session:", error);
    }
  }
}

// Save session to chrome.storage
export async function saveSession(): Promise<void> {
  if (typeof chrome !== "undefined" && chrome.storage) {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (session) {
        await chrome.storage.local.set({ supabase_session: session });
      }
    } catch (error) {
      console.warn("Failed to save Supabase session:", error);
    }
  }
}

// Clear session from chrome.storage
export async function clearSession(): Promise<void> {
  if (typeof chrome !== "undefined" && chrome.storage) {
    try {
      await chrome.storage.local.remove(["supabase_session"]);
    } catch (error) {
      console.warn("Failed to clear Supabase session:", error);
    }
  }
}

// Google OAuth sign in with Supabase Auth
export async function signInWithGoogle(): Promise<{ data: any; error: any }> {
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: "google",
      options: {
        redirectTo: `chrome-extension://${chrome.runtime.id}/`,
        queryParams: {
          access_type: "offline",
          prompt: "consent",
        },
      },
    });

    if (error) {
      console.error("Supabase Google OAuth error:", error);
      return { data: null, error };
    }

    // Save session after successful authentication
    await saveSession();
    return { data, error: null };
  } catch (error) {
    console.error("Google sign-in error:", error);
    return { data: null, error };
  }
}

// Sign out
export async function signOut(): Promise<{ error: any }> {
  try {
    const { error } = await supabase.auth.signOut();
    await clearSession();
    return { error };
  } catch (error) {
    console.error("Sign out error:", error);
    return { error };
  }
}

// Get current user
export async function getCurrentUser() {
  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();
    return { user, error };
  } catch (error) {
    console.error("Get current user error:", error);
    return { user: null, error };
  }
}

// Get current session
export async function getCurrentSession() {
  try {
    const {
      data: { session },
      error,
    } = await supabase.auth.getSession();
    return { session, error };
  } catch (error) {
    console.error("Get current session error:", error);
    return { session: null, error };
  }
}

// Listen for auth state changes and save session
supabase.auth.onAuthStateChange(async (event, session) => {
  console.log("Supabase auth state changed:", event, session?.user?.email);

  if (event === "SIGNED_IN" && session) {
    await saveSession();
  } else if (event === "SIGNED_OUT") {
    await clearSession();
  }
});

// Initialize auth on module load
initializeAuth().catch(console.error);
