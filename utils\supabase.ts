// Simple Supabase client using fetch API for Chrome extension compatibility
const SUPABASE_URL = import.meta.env.WXT_SUPABASE_URL || '';
const SUPABASE_ANON_KEY = import.meta.env.WXT_SUPABASE_ANON_KEY || '';
const SUPABASE_EMAIL = import.meta.env.WXT_SUPABASE_EMAIL || '';
const SUPABASE_PASSWORD = import.meta.env.WXT_SUPABASE_PASSWORD || '';

export interface SupabaseResponse<T = any> {
  data: T | null;
  error: any;
}

// Helper to get Authorization header
async function getAuthHeader(): Promise<string> {
  if (SUPABASE_EMAIL && SUPABASE_PASSWORD) {
    const token = await getAccessToken(SUPABASE_EMAIL, SUPABASE_PASSWORD);
    if (token) return token;
  }
  return SUPABASE_ANON_KEY;
}

// Simple Supabase client implementation using fetch
export const supabase = {
  from: (table: string) => ({
    select: (columns: string = '*') => ({
      eq: (column: string, value: any) => ({
        single: async (): Promise<SupabaseResponse> => {
          const authHeader = await getAuthHeader();
          try {
            const response = await fetch(`${SUPABASE_URL}/rest/v1/${table}?${column}=eq.${encodeURIComponent(value)}&select=${columns}`, {
              headers: {
                'apikey': SUPABASE_ANON_KEY,
                'Authorization': `Bearer ${authHeader}`,
                'Content-Type': 'application/json',
                'Prefer': 'return=representation'
              }
            });

            if (!response.ok) {
              if (response.status === 406) {
                // No rows returned (PGRST116 equivalent)
                return { data: null, error: { code: 'PGRST116', message: 'No rows returned' } };
              }
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return { data: Array.isArray(data) ? data[0] : data, error: null };
          } catch (error) {
            return { data: null, error };
          }
        },
        select: async (): Promise<SupabaseResponse> => {
          const authHeader = await getAuthHeader();
          try {
            const response = await fetch(`${SUPABASE_URL}/rest/v1/${table}?${column}=eq.${encodeURIComponent(value)}&select=${columns}`, {
              headers: {
                'apikey': SUPABASE_ANON_KEY,
                'Authorization': `Bearer ${authHeader}`,
                'Content-Type': 'application/json',
                // 'Prefer': 'return=representation'
              }
            });

            if (!response.ok) {
              if (response.status === 406) {
                // No rows returned (PGRST116 equivalent)
                return { data: [], error: { code: 'PGRST116', message: 'No rows returned' } };
              }
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return { data: Array.isArray(data) ? data : [data], error: null };
          } catch (error) {
            return { data: null, error };
          }
        }
      })
    }),
    insert: (data: any) => ({
      select: async (): Promise<SupabaseResponse> => {
        const authHeader = await getAuthHeader();
        try {
          const response = await fetch(`${SUPABASE_URL}/rest/v1/${table}`, {
            method: 'POST',
            headers: {
              'apikey': SUPABASE_ANON_KEY,
              'Authorization': `Bearer ${authHeader}`,
              'Content-Type': 'application/json',
              // 'Prefer': 'return=representation'
            },
            body: JSON.stringify(data)
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();
          return { data: Array.isArray(result) ? result[0] : result, error: null };
        } catch (error) {
          return { data: null, error };
        }
      }
    }),
    update: (data: any) => ({
      eq: (column: string, value: any) => ({
        select: async (): Promise<SupabaseResponse> => {
          const authHeader = await getAuthHeader();
          try {
            const response = await fetch(`${SUPABASE_URL}/rest/v1/${table}?${column}=eq.${encodeURIComponent(value)}`, {
              method: 'PATCH',
              headers: {
                'apikey': SUPABASE_ANON_KEY,
                'Authorization': `Bearer ${authHeader}`,
                'Content-Type': 'application/json',
                'Prefer': 'return=representation'
              },
              body: JSON.stringify(data)
            });

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            return { data: Array.isArray(result) ? result[0] : result, error: null };
          } catch (error) {
            return { data: null, error };
          }
        }
      })
    })
  })
};

interface AccessTokenResponse {
  access_token: string;
  expires_in: number;
  expires_at: number;
  refresh_token?: string;
  token_type: string;
  user: any;
}

const ACCESS_TOKEN_KEY = 'supabase_access_token';
const ACCESS_TOKEN_EXPIRES_AT_KEY = 'supabase_access_token_expires_at';

/**
 * Get a valid access token for the user (using password grant).
 * Caches the token and refreshes if expired.
 * @param email User email
 * @param password User password
 * @returns access_token string
 */
export async function getAccessToken(email: string, password: string): Promise<string | null> {
  const hasLocalStorage = typeof window !== 'undefined' && typeof window.localStorage !== 'undefined';
  let cachedToken: string | null = null;
  let expiresAt: number = 0;
  const now = Math.floor(Date.now() / 1000);

  if (hasLocalStorage) {
    cachedToken = localStorage.getItem(ACCESS_TOKEN_KEY);
    expiresAt = Number(localStorage.getItem(ACCESS_TOKEN_EXPIRES_AT_KEY));
    if (cachedToken && expiresAt && now < expiresAt - 60) {
      return cachedToken;
    }
  }

  // Fetch new token
  const response = await fetch(`${SUPABASE_URL}/auth/v1/token?grant_type=password`, {
    method: 'POST',
    headers: {
      'apikey': SUPABASE_ANON_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email, password })
  });

  if (!response.ok) {
    console.error('Failed to get access token', await response.text());
    return null;
  }

  const data: AccessTokenResponse = await response.json();
  if (hasLocalStorage) {
    localStorage.setItem(ACCESS_TOKEN_KEY, data.access_token);
    localStorage.setItem(ACCESS_TOKEN_EXPIRES_AT_KEY, (now + data.expires_in).toString());
  }
  return data.access_token;
} 