{"name": "Prompt Pilot", "description": "manifest.json description", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "tsc --noEmit", "postinstall": "wxt prepare"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@supabase/supabase-js": "^2.52.1", "firebase": "^11.6.1", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/chrome": "^0.0.318", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "@wxt-dev/module-react": "^1.1.3", "typescript": "^5.8.3", "wxt": "^0.20.6"}}