-- Optional improvements to users table (only run if needed)

-- 1. Add constraints for better data integrity
ALTER TABLE users 
ADD CONSTRAINT users_email_not_empty 
CHECK (email != '');

-- 2. Add a status field for user management (optional)
ALTER TABLE users 
ADD COLUMN status VARCHAR(20) DEFAULT 'active' 
CHECK (status IN ('active', 'inactive', 'suspended'));

-- 3. Add last_login tracking (optional)
ALTER TABLE users 
ADD COLUMN last_login TIMESTAMP WITH TIME ZONE;

-- 4. Create a function to update last_login
CREATE OR REPLACE FUNCTION update_user_last_login(user_email VARCHAR)
RETURNS VOID AS $$
BEGIN
  UPDATE users 
  SET last_login = NOW() 
  WHERE email = user_email;
END;
$$ LANGUAGE plpgsql;

-- 5. Add better indexing for common queries (if needed)
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- 6. Add a view for active users (optional)
CREATE OR REPLACE VIEW active_users AS
SELECT 
  id, 
  email, 
  name, 
  picture, 
  google_id, 
  created_at, 
  updated_at,
  last_login
FROM users 
WHERE status = 'active';

-- Note: These are optional improvements. 
-- Your current users table should work fine without these changes.
