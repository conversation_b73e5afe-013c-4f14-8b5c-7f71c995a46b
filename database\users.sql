-- Simple users table setup with permissive <PERSON><PERSON> for testing
-- This allows anonymous users to insert and read data

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  name <PERSON><PERSON><PERSON><PERSON>(255),
  picture VARCHAR(500),
  google_id VARCHAR(255) UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON>reate indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create permissive policies for testing
-- Allow anonymous users to read all data
CREATE POLICY "Enable read access for all users" ON users
  FOR SELECT USING (true);

-- Allow anonymous users to insert data
CREATE POLICY "Enable insert for authenticated users only" ON users
  FOR INSERT WITH CHECK (true);

-- Allow anonymous users to update data
CREATE POLICY "Enable update for authenticated users only" ON users
  FOR UPDATE USING (true);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at
  BEFORE UPDATE ON users
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column(); 