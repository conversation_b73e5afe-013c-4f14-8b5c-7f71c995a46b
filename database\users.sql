-- Users table setup for Chrome extension with custom authentication
-- Optimized for Google OAuth integration

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  name <PERSON><PERSON><PERSON><PERSON>(255),
  picture VARCHAR(500),
  google_id VARCHAR(255) UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for fast lookups
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id);

-- Add constraints for data integrity
ALTER TABLE users ADD CONSTRAINT users_email_not_empty CHECK (email != '');
ALTER TABLE users ADD CONSTRAINT users_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policies for Chrome extension authentication
-- These are permissive since we handle authentication at the application level

-- Allow reading user data (needed for user lookups)
CREATE POLICY "Enable read access for all users" ON users
  FOR SELECT USING (true);

-- Allow inserting new users (needed for user registration)
CREATE POLICY "Enable insert for all users" ON users
  FOR INSERT WITH CHECK (true);

-- Allow updating user data (needed for profile updates)
CREATE POLICY "Enable update for all users" ON users
  FOR UPDATE USING (true);

-- Allow deleting users (optional, for user management)
CREATE POLICY "Enable delete for all users" ON users
  FOR DELETE USING (true);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at
  BEFORE UPDATE ON users
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column(); 