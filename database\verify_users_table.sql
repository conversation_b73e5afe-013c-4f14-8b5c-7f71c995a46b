-- Diagnostic script to verify users table setup
-- Run this in your Supabase SQL editor to check everything is working

-- 1. Check table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;

-- 2. Check indexes
SELECT 
    indexname, 
    indexdef 
FROM pg_indexes 
WHERE tablename = 'users';

-- 3. Check RLS policies
SELECT 
    schemaname, 
    tablename, 
    policyname, 
    permissive, 
    roles, 
    cmd, 
    qual, 
    with_check
FROM pg_policies 
WHERE tablename = 'users';

-- 4. Check if RLS is enabled
SELECT 
    schemaname, 
    tablename, 
    rowsecurity 
FROM pg_tables 
WHERE tablename = 'users';

-- 5. Sample data check (if any users exist)
SELECT 
    id, 
    email, 
    name, 
    google_id, 
    created_at, 
    updated_at 
FROM users 
LIMIT 5;

-- 6. Count total users
SELECT COUNT(*) as total_users FROM users;
