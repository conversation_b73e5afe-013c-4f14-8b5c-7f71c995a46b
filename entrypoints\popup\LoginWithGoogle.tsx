import { Box, Button, Typography, IconButton } from '@mui/material';
import GoogleIcon from '@mui/icons-material/Google';
import CloseIcon from '@mui/icons-material/Close';

export default function LoginWithGoogle({ onLogin, onClose, loading }: { onLogin: () => void; onClose: () => void; loading: boolean }) {
  return (
    <Box sx={{
      width: 350,
      minHeight: 220,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      p: 3,
      position: 'relative',
      background: '#fff',
      borderRadius: 2,
      boxShadow: 2,
    }}>
      <IconButton
        size="small"
        onClick={onClose}
        sx={{ position: 'absolute', top: 8, right: 8 }}
        aria-label="Close"
      >
        <CloseIcon />
      </IconButton>
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
        Sign in to PromptPilot
      </Typography>
      <Typography sx={{ mb: 3, color: '#666', fontSize: 15, textAlign: 'center' }}>
        Log in with Google to sync your data and access all features.<br />
        (Optional – you can close this and use the app without logging in)
      </Typography>
      <Button
        variant="contained"
        color="primary"
        startIcon={<GoogleIcon />}
        onClick={onLogin}
        disabled={loading}
        sx={{ minWidth: 220, fontWeight: 600, fontSize: 16 }}
      >
        {loading ? 'Signing in...' : 'Login with Google'}
      </Button>
    </Box>
  );
} 