import { supabase, type SupabaseResponse } from './supabase';
import { getAuthState } from './auth';
import type { PromptData } from '../entrypoints/popup/PromptSaveModal';

export interface PromptLibraryItem extends PromptData {
  id: string;
  user_id?: string;
  isFavorite: boolean;
  createdAt: number;
  updatedAt: number;
}

// Get all prompts for the current user (local or DB)
export async function getPromptLibrary(): Promise<PromptLibraryItem[]> {
  const auth = await getAuthState();
  if (auth.isAuthenticated && auth.userInfo) {
    // Get user_id from DB
    const userResult: SupabaseResponse = await supabase
      .from('users')
      .select('id')
      .eq('email', auth.userInfo.email)
      .single();
    const user = userResult.data;
    const error = userResult.error;
    if (error || !user) return [];
    const { data, error: promptError } = await supabase
      .from('prompts')
      .select('*')
      .eq('user_id', user.id)
      .select();
    console.log('>>> getPromptLibrary >>> data', data);
    if (promptError || !data) return [];
    return data.map((item: any) => ({
      ...item,
      createdAt: new Date(item.created_at).getTime(),
      updatedAt: new Date(item.updated_at).getTime(),
      isFavorite: item.is_favorite,
      user_id: item.user_id,
      id: item.id,
    }));
  } else {
    // Guest: use local storage
    const result = await chrome.storage.local.get(['promptLibrary']);
    return Array.isArray(result.promptLibrary) ? result.promptLibrary : [];
  }
}

// Add a prompt to the library (local or DB)
export async function addPromptLibrary(prompt: PromptData): Promise<void> {
  const now = Date.now();
  const auth = await getAuthState();
  if (auth.isAuthenticated && auth.userInfo) {
    // Get user_id from DB
    const userResult: SupabaseResponse = await supabase
      .from('users')
      .select('id')
      .eq('email', auth.userInfo.email)
      .single();
    const user = userResult.data;
    const error = userResult.error;
    if (error || !user) return;
    await supabase
      .from('prompts')
      .insert({
        user_id: user.id,
        title: prompt.title,
        content: prompt.content,
        tags: prompt.tags,
        folder: prompt.folder,
        is_favorite: false,
      })
      .select();
  } else {
    // Guest: use local storage
    const result = await chrome.storage.local.get(['promptLibrary']);
    const library = Array.isArray(result.promptLibrary) ? result.promptLibrary : [];
    const newPrompt = {
      id: now.toString(),
      ...prompt,
      isFavorite: false,
      createdAt: now,
      updatedAt: now
    };
    const updated = [newPrompt, ...library];
    await chrome.storage.local.set({ promptLibrary: updated });
  }
} 