# 🔧 Integration Guide

## Quick Integration

To add the inline-like prompt optimizer to your existing content script, follow these simple steps:

### Step 1: Add the Import

Add this line to the top of your existing `entrypoints/content/index.ts`:

```typescript
// Add this import at the top of the file
import './inline-loader';
```

### Step 2: That's It! 🎉

The inline optimizer will automatically:
- ✅ Detect supported chatbot platforms
- ✅ Inject the optimize button when users start typing
- ✅ Handle all the UI interactions
- ✅ Work alongside your existing functionality

## Manual Integration (Optional)

If you prefer more control, you can manually initialize the optimizer:

```typescript
// In your existing content script
import { initializeInlineOptimizer } from './inline-loader';

// Call this when you want to initialize the optimizer
initializeInlineOptimizer();
```

## Testing the Integration

1. **Build the extension:**
   ```bash
   npm run dev
   ```

2. **Load the extension in your browser**

3. **Visit supported platforms:**
   - ChatGPT: `https://chatgpt.com`
   - Claude: `https://claude.ai`
   - Gemini: `https://gemini.google.com`
   - Perplexity: `https://perplexity.ai`

4. **Start typing in any input field** - you should see the ✨ Optimize button appear!

## Customization

### Modify the Optimization Logic

Edit `entrypoints/content/inline-optimizer.tsx` and replace the mock `optimizePrompt` function:

```typescript
// Replace this mock function with your real API call
const optimizePrompt = async (prompt: string): Promise<OptimizedPrompt[]> => {
  const response = await fetch('your-api-endpoint', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ prompt })
  });
  
  return response.json();
};
```

### Add New Platform Support

Edit `utils/configs.ts` and add your platform:

```typescript
{
  name: 'YourPlatform',
  selectors: ['textarea.your-input', 'input[type="text"]'],
  parentSelector: 'div.input-container',
  buttonContainerClass: 'inline-buttons-yourplatform',
  urlCheck: (url: string) => url.includes('yourplatform.com')
}
```

## Troubleshooting

### Check Console for Errors
Open browser dev tools and look for:
- `✨ Inline Optimizer: Successfully initialized` ✅
- Any error messages ❌

### Common Issues

1. **Button not appearing:**
   - Check if the platform is supported in `utils/configs.ts`
   - Verify the selectors match the actual DOM elements

2. **Styling conflicts:**
   - The optimizer uses Material-UI with high z-index values
   - Check for CSS conflicts with the host page

3. **Performance issues:**
   - The optimizer only initializes when needed
   - Uses debounced event handlers to prevent excessive calls

## Advanced Configuration

### Environment Variables
Add these to your `.env` file for real API integration:

```bash
WXT_OPENAI_API_KEY=your_openai_key
WXT_GEMINI_API_KEY=your_gemini_key
```

### Custom Styling
Override the default styles by modifying the styled components in `inline-optimizer.tsx`:

```typescript
const CustomOptimizeButton = styled(OptimizeButton)(({ theme }) => ({
  // Your custom styles here
  backgroundColor: '#your-color',
  borderRadius: '20px',
}));
```

## Support

If you encounter any issues:
1. Check the browser console for error messages
2. Verify the platform is supported in the configs
3. Test with the demo page first: `public/demo.html`
4. Create an issue with detailed error information

---

**✨ Happy integrating! ✨** 