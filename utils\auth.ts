import { UserInfo, AuthState } from '../types/auth';
import { OAUTH_CONFIG } from './constants';
import { upsertUser } from './userService';

// Get user info from Google API
async function getUserInfo(accessToken: string): Promise<UserInfo> {
  const response = await fetch('https://www.googleapis.com/oauth2/v3/userinfo', {
    headers: {
      Authorization: `Bearer ${accessToken}`
    }
  });

  if (!response.ok) {
    throw new Error('Failed to get user info');
  }

  return response.json();
}

// Save auth state to chrome.storage.local and database
export async function saveAuthState(accessToken: string, userInfo: UserInfo): Promise<void> {
  try {
    // Store user in database
    // console.log('Storing user in database:', userInfo.email);
    await upsertUser(userInfo);
    // console.log('User stored successfully in database');
  } catch (error) {
    console.error('Error storing user in database:', error);
    // Continue with auth state even if database storage fails
  }

  const authState: AuthState = {
    isAuthenticated: true,
    userInfo,
    accessToken
  };

  await chrome.storage.local.set({ authState });
  
  // Broadcast auth state change to all extension contexts with error handling
  try {
    await browser.runtime.sendMessage({ type: 'AUTH_STATE_CHANGED', authState });
  } catch (error) {
    console.log('Failed to broadcast auth state change:', error);
  }
}

// Clear auth state from chrome.storage.local
export async function clearAuthState(): Promise<void> {
  await chrome.storage.local.remove(['authState']);
  
  // Broadcast auth state change to all extension contexts with error handling
  try {
    await browser.runtime.sendMessage({ type: 'AUTH_STATE_CHANGED', authState: null });
  } catch (error) {
    console.log('Failed to broadcast auth state clear:', error);
  }
}

// Get auth state from chrome.storage.local
export async function getAuthState(): Promise<AuthState> {
  const result = await chrome.storage.local.get(['authState']);
  return result.authState || { isAuthenticated: false, userInfo: null, accessToken: null };
}

// Handle Google Sign In
export async function handleGoogleSignIn(): Promise<AuthState> {
  try {
    const redirectUri = encodeURIComponent(OAUTH_CONFIG.redirectUri);
    
    const authUrl = `https://accounts.google.com/o/oauth2/auth?` +
      `client_id=${OAUTH_CONFIG.clientId}` +
      `&response_type=token` +
      `&redirect_uri=${redirectUri}` +
      `&scope=${OAUTH_CONFIG.scopes.join('%20')}`;
    
    const responseUrl = await new Promise<string>((resolve, reject) => {
      chrome.identity.launchWebAuthFlow(
        { url: authUrl, interactive: true },
        (responseUrl) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else if (responseUrl) {
            resolve(responseUrl);
          } else {
            reject(new Error('Failed to get auth response URL'));
          }
        }
      );
    });
    
    const hashParams = new URLSearchParams(responseUrl.split('#')[1]);
    const accessToken = hashParams.get('access_token');
    
    if (!accessToken) {
      throw new Error('No access token found in the response');
    }
    
    const userInfo = await getUserInfo(accessToken);
    await saveAuthState(accessToken, userInfo);
    
    return {
      isAuthenticated: true,
      userInfo,
      accessToken
    };
  } catch (error) {
    console.error('Error signing in with Google:', error);
    throw error;
  }
}

// Handle Sign Out
export async function handleSignOut(): Promise<void> {
  await clearAuthState();
} 