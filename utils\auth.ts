import { UserInfo, AuthState } from "../types/auth";
import { OAUTH_CONFIG } from "./constants";
import { upsertUser } from "./userService";
import {
  supabase,
  signInWithGoogle as supabaseSignInWithGoogle,
  signOut as supabaseSignOut,
  getCurrentUser,
  getCurrentSession,
  createSupabaseUser,
} from "./supabase";

// Get user info from Google API
async function getUserInfo(accessToken: string): Promise<UserInfo> {
  const response = await fetch(
    "https://www.googleapis.com/oauth2/v3/userinfo",
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to get user info");
  }

  return response.json();
}

// Convert Supabase user to UserInfo format
function convertSupabaseUserToUserInfo(user: any): UserInfo {
  return {
    email: user.email || "",
    name: user.user_metadata?.full_name || user.user_metadata?.name || "",
    picture:
      user.user_metadata?.avatar_url || user.user_metadata?.picture || "",
    sub: user.id || user.user_metadata?.sub || "",
  };
}

// Save auth state to chrome.storage.local and database
export async function saveAuthState(
  accessToken: string,
  userInfo: UserInfo
): Promise<void> {
  try {
    // Store user in database
    // console.log('Storing user in database:', userInfo.email);
    await upsertUser(userInfo);
    // console.log('User stored successfully in database');
  } catch (error) {
    console.error("Error storing user in database:", error);
    // Continue with auth state even if database storage fails
  }

  const authState: AuthState = {
    isAuthenticated: true,
    userInfo,
    accessToken,
  };

  await chrome.storage.local.set({ authState });

  // Broadcast auth state change to all extension contexts with error handling
  try {
    await browser.runtime.sendMessage({
      type: "AUTH_STATE_CHANGED",
      authState,
    });
  } catch (error) {
    console.log("Failed to broadcast auth state change:", error);
  }
}

// Clear auth state from chrome.storage.local
export async function clearAuthState(): Promise<void> {
  await chrome.storage.local.remove(["authState"]);

  // Broadcast auth state change to all extension contexts with error handling
  try {
    await browser.runtime.sendMessage({
      type: "AUTH_STATE_CHANGED",
      authState: null,
    });
  } catch (error) {
    console.log("Failed to broadcast auth state clear:", error);
  }
}

// Get auth state from chrome.storage.local
export async function getAuthState(): Promise<AuthState> {
  const result = await chrome.storage.local.get(["authState"]);
  return (
    result.authState || {
      isAuthenticated: false,
      userInfo: null,
      accessToken: null,
    }
  );
}

// Enhanced Google Sign In with Supabase Auth integration
export async function handleGoogleSignIn(): Promise<AuthState> {
  try {
    // First, try to get existing Supabase session
    const { session } = await getCurrentSession();
    if (session?.user) {
      const userInfo = convertSupabaseUserToUserInfo(session.user);
      await saveAuthState(session.access_token, userInfo);
      return {
        isAuthenticated: true,
        userInfo,
        accessToken: session.access_token,
      };
    }

    // If no session, perform Chrome extension OAuth flow
    const redirectUri = encodeURIComponent(OAUTH_CONFIG.redirectUri);

    const authUrl =
      `https://accounts.google.com/o/oauth2/auth?` +
      `client_id=${OAUTH_CONFIG.clientId}` +
      `&response_type=token` +
      `&redirect_uri=${redirectUri}` +
      `&scope=${OAUTH_CONFIG.scopes.join("%20")}`;

    const responseUrl = await new Promise<string>((resolve, reject) => {
      chrome.identity.launchWebAuthFlow(
        { url: authUrl, interactive: true },
        (responseUrl) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else if (responseUrl) {
            resolve(responseUrl);
          } else {
            reject(new Error("Failed to get auth response URL"));
          }
        }
      );
    });

    const hashParams = new URLSearchParams(responseUrl.split("#")[1]);
    const accessToken = hashParams.get("access_token");

    if (!accessToken) {
      throw new Error("No access token found in the response");
    }

    const userInfo = await getUserInfo(accessToken);

    // Create a custom session for Chrome extension compatibility
    try {
      const { data, error } = await createSupabaseUser(userInfo, accessToken);

      if (error) {
        console.warn("Failed to create Supabase session:", error);
      } else {
        console.log("Successfully created Chrome extension session");
      }
    } catch (supabaseError) {
      console.warn("Supabase session creation failed:", supabaseError);
    }

    // Always save to local auth state as well
    await saveAuthState(accessToken, userInfo);

    return {
      isAuthenticated: true,
      userInfo,
      accessToken,
    };
  } catch (error) {
    console.error("Error signing in with Google:", error);
    throw error;
  }
}

// Handle Sign Out
export async function handleSignOut(): Promise<void> {
  try {
    // Sign out from Supabase
    await supabaseSignOut();
  } catch (error) {
    console.warn("Failed to sign out from Supabase:", error);
  }

  // Clear local auth state
  await clearAuthState();
}
