declare namespace chrome {
  namespace storage {
    interface StorageChange {
      oldValue?: any;
      newValue?: any;
    }

    interface StorageArea {
      get(
        keys?: string | string[] | object | null
      ): Promise<{ [key: string]: any }>;
      set(items: object): Promise<void>;
      remove(keys: string | string[]): Promise<void>;
      clear(): Promise<void>;
      onChanged: {
        addListener(
          callback: (changes: { [key: string]: StorageChange }) => void
        ): void;
        removeListener(
          callback: (changes: { [key: string]: StorageChange }) => void
        ): void;
      };
    }

    const local: StorageArea;
    const sync: StorageArea;
  }

  namespace tabs {
    interface Tab {
      id?: number;
      url?: string;
      title?: string;
    }

    function create(
      createProperties: { url: string },
      callback?: (tab: Tab) => void
    ): void;
    function sendMessage(
      tabId: number,
      message: any,
      callback?: (response: any) => void
    ): void;
    function query(queryInfo: any): Promise<Tab[]>;
  }

  namespace identity {
    function getRedirectURL(path?: string): string;
    function launchWebAuthFlow(
      details: { url: string; interactive: boolean },
      callback: (responseUrl?: string) => void
    ): void;
  }

  namespace runtime {
    interface LastError {
      message?: string;
    }
    const lastError: LastError | undefined;
    function getURL(path: string): string;
    const id: string;
    function sendMessage(
      message: any,
      callback?: (response: any) => void
    ): void;
    const onInstalled: {
      addListener(callback: () => void): void;
    };
  }

  namespace scripting {
    function executeScript(
      details: {
        target: { tabId: number };
        files: string[];
      },
      callback?: (results: any[]) => void
    ): void;
  }

  namespace action {
    function openPopup(): void;
  }
}

declare namespace browser {
  namespace storage {
    interface StorageChange {
      oldValue?: any;
      newValue?: any;
    }

    interface StorageArea {
      get(
        keys?: string | string[] | object | null
      ): Promise<{ [key: string]: any }>;
      set(items: object): Promise<void>;
      remove(keys: string | string[]): Promise<void>;
      clear(): Promise<void>;
      onChanged: {
        addListener(
          callback: (changes: { [key: string]: StorageChange }) => void
        ): void;
        removeListener(
          callback: (changes: { [key: string]: StorageChange }) => void
        ): void;
      };
    }

    const local: StorageArea;
    const sync: StorageArea;
  }

  namespace tabs {
    interface Tab {
      id?: number;
      url?: string;
      title?: string;
    }

    function create(createProperties: { url: string }): Promise<Tab>;
    function sendMessage(tabId: number, message: any): Promise<any>;
    function query(queryInfo: any): Promise<Tab[]>;
  }

  namespace identity {
    function getRedirectURL(path?: string): string;
    function launchWebAuthFlow(
      details: { url: string; interactive: boolean },
      callback: (responseUrl?: string) => void
    ): void;
  }

  namespace runtime {
    interface LastError {
      message?: string;
    }
    const lastError: LastError | undefined;
    function sendMessage(message: any): Promise<any>;
    const onMessage: {
      addListener(
        callback: (
          message: any,
          sender: any,
          sendResponse: (response?: any) => void
        ) => void
      ): void;
    };
    const onInstalled: {
      addListener(callback: () => void): void;
    };
  }

  namespace action {
    function openPopup(): void;
  }
}
