import { supabase, getCurrentUser } from "./supabase";
import type { UserInfo } from "../types/auth";

export interface DatabaseUser {
  id: string;
  email: string;
  name: string | null;
  picture: string | null;
  google_id: string | null;
  created_at: string;
  updated_at: string;
}

// Create or update user in database
export async function upsertUser(userInfo: UserInfo): Promise<DatabaseUser> {
  try {
    // console.log('Upserting user:', userInfo.email);

    const { data: existingUser, error: selectError } = await supabase
      .from("users")
      .select("*")
      .eq("email", userInfo.email)
      .single();

    // console.log('Existing user check result:', { existingUser, selectError });

    if (selectError && selectError.code !== "PGRST116") {
      // PGRST116 is "not found" error, which is expected for new users
      throw new Error(`Error checking existing user: ${selectError.message}`);
    }

    if (existingUser) {
      // Update existing user
      // console.log('Updating existing user');
      const { data: updatedUser, error: updateError } = await supabase
        .from("users")
        .update({
          name: userInfo.name,
          picture: userInfo.picture,
          google_id: userInfo.sub,
          updated_at: new Date().toISOString(),
        })
        .eq("email", userInfo.email)
        .select();

      // console.log('Update result:', { updatedUser, updateError });

      if (updateError) {
        throw new Error(`Error updating user: ${updateError.message}`);
      }

      return updatedUser[0];
    } else {
      // Create new user
      // console.log('Creating new user');
      const { data: newUser, error: insertError } = await supabase
        .from("users")
        .insert({
          email: userInfo.email,
          name: userInfo.name,
          picture: userInfo.picture,
          google_id: userInfo.sub,
        })
        .select();

      // console.log('Insert result:', { newUser, insertError });

      if (insertError) {
        throw new Error(`Error creating user: ${insertError.message}`);
      }

      return newUser[0];
    }
  } catch (error) {
    console.error("Error upserting user:", error);
    throw error;
  }
}

// Get user by email
export async function getUserByEmail(
  email: string
): Promise<DatabaseUser | null> {
  try {
    const { data: user, error } = await supabase
      .from("users")
      .select("*")
      .eq("email", email)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        // User not found
        return null;
      }
      throw new Error(`Error fetching user: ${error.message}`);
    }

    return user;
  } catch (error) {
    console.error("Error getting user by email:", error);
    throw error;
  }
}

// Get user by Google ID
export async function getUserByGoogleId(
  googleId: string
): Promise<DatabaseUser | null> {
  try {
    const { data: user, error } = await supabase
      .from("users")
      .select("*")
      .eq("google_id", googleId)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        // User not found
        return null;
      }
      throw new Error(`Error fetching user: ${error.message}`);
    }

    return user;
  } catch (error) {
    console.error("Error getting user by Google ID:", error);
    throw error;
  }
}

// Update user profile
export async function updateUserProfile(
  email: string,
  updates: Partial<Pick<DatabaseUser, "name" | "picture">>
): Promise<DatabaseUser> {
  try {
    const { data: user, error } = await supabase
      .from("users")
      .update(updates)
      .eq("email", email)
      .select();

    if (error) {
      throw new Error(`Error updating user profile: ${error.message}`);
    }

    return user[0];
  } catch (error) {
    console.error("Error updating user profile:", error);
    throw error;
  }
}

// Get current authenticated user from Supabase Auth
export async function getCurrentAuthenticatedUser(): Promise<DatabaseUser | null> {
  try {
    const { user, error } = await getCurrentUser();

    if (error || !user) {
      return null;
    }

    // Try to get user from our custom users table
    const { data: dbUser, error: dbError } = await supabase
      .from("users")
      .select("*")
      .eq("email", user.email)
      .single();

    if (dbError && dbError.code !== "PGRST116") {
      console.error("Error fetching user from database:", dbError);
      return null;
    }

    return dbUser;
  } catch (error) {
    console.error("Error getting current authenticated user:", error);
    return null;
  }
}

// Sync Supabase Auth user with our custom users table
export async function syncSupabaseUser(): Promise<DatabaseUser | null> {
  try {
    const { user, error } = await getCurrentUser();

    if (error || !user) {
      return null;
    }

    const userInfo: UserInfo = {
      email: user.email || "",
      name: user.user_metadata?.full_name || user.user_metadata?.name || "",
      picture:
        user.user_metadata?.avatar_url || user.user_metadata?.picture || "",
      sub: user.id,
    };

    return await upsertUser(userInfo);
  } catch (error) {
    console.error("Error syncing Supabase user:", error);
    return null;
  }
}
