import { useState, useEffect } from 'react';
import { Button, CircularProgress, Typography, Box, Alert, Snackbar, <PERSON>bs, Tab, IconButton } from '@mui/material';
import GoogleIcon from '@mui/icons-material/Google';
import { handleSignOut, getAuthState } from '@/utils/auth';
import type { AuthState } from '@/types/auth';
import SavedResponses from './SavedResponses';
import './App.css';
import SettingsIcon from '@mui/icons-material/Settings';
import CloseIcon from '@mui/icons-material/Close';
import LibraryBooksIcon from '@mui/icons-material/LibraryBooks';
import ForumIcon from '@mui/icons-material/Forum';
import ChatIcon from '@mui/icons-material/Chat';
import GroupIcon from '@mui/icons-material/Group';
import Logo from '@/assets/react.svg';
import PromptPanel from './PromptPanel';
import LibraryPanel from './LibraryPanel';
import CommunityPanel from './CommunityPanel';
import SettingsPanel from './SettingsPanel';
import LoginWithGoogle from './LoginWithGoogle';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import { addPromptLibrary } from '@/utils/promptLibrary';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 0 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

// TopBar component (inline for now)
function TopBar({ onSettings, onClose, user, onLogout }: { onSettings: () => void; onClose: () => void; user: any; onLogout: () => void }) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleUserIconClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  const handleLogoutClick = () => {
    handleMenuClose();
    onLogout();
  };

  return (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      height: 48,
      px: 1,
      borderBottom: '1px solid #e0e0e0',
      background: '#fff',
      position: 'sticky',
      top: 0,
      zIndex: 10
    }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <img src={chrome.runtime.getURL('/icon/48.png')} alt="PromptPilot Logo" style={{ height: 32, width: 32 }} />
        <Typography variant="h6" sx={{ fontWeight: 700, color: '#e0904e', fontSize: '1.1rem' }}>
          PromptPilot
        </Typography>
      </Box>
      <Box>
        {user && (
          <>
            <IconButton size="small" onClick={handleUserIconClick} sx={{ p: 0, mr: 1 }}>
              {user.picture ? (
                <img src={user.picture} alt="User" style={{ width: 32, height: 32, borderRadius: '50%' }} />
              ) : (
                <AccountCircleIcon fontSize="large" />
              )}
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={open}
              onClose={handleMenuClose}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
              transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            >
              <MenuItem onClick={handleLogoutClick}>Log out</MenuItem>
            </Menu>
          </>
        )}
        <IconButton size="small" onClick={onSettings}>
          <SettingsIcon />
        </IconButton>
        <IconButton size="small" onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </Box>
    </Box>
  );
}

export default function App() {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    userInfo: null,
    accessToken: null
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [promptPanelValue, setPromptPanelValue] = useState('');
  const [showLoginScreen, setShowLoginScreen] = useState(true);

  // Initialize auth state and set up listeners
  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        console.log('Loading initial auth state...');
        const state = await getAuthState();
        console.log('Initial auth state:', state);
        if (mounted) {
          setAuthState(state);
        }
      } catch (error) {
        console.error('Error loading auth state:', error);
        if (mounted) {
          setError('Failed to load authentication state');
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    // Handle storage changes
    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }) => {
      if (changes.authState && mounted) {
        console.log('Auth state changed from storage:', changes.authState.newValue);
        const newState = changes.authState.newValue || {
          isAuthenticated: false,
          userInfo: null,
          accessToken: null
        };
        setAuthState(newState);

        // Show success message when user logs in
        if (newState.isAuthenticated && !authState.isAuthenticated) {
          setSuccess('Successfully signed in!');
        }
      }
    };

    // Handle messages from background service worker
    const handleMessage = (message: any) => {
      if (!mounted) return;

      if (message.type === 'AUTH_STATE_CHANGED') {
        console.log('Auth state changed from message:', message.authState);
        const newState = message.authState || {
          isAuthenticated: false,
          userInfo: null,
          accessToken: null
        };
        setAuthState(newState);

        // Show success message when user logs in
        if (newState.isAuthenticated && !authState.isAuthenticated) {
          setSuccess('Successfully signed in!');
        }
      } else if (message.type === 'GOOGLE_LOGIN_RESULT') {
        if (message.success) {
          console.log('Login successful:', message.authState);
          setAuthState(message.authState);
          setSuccess('Successfully signed in!');
        } else {
          console.error('Login failed:', message.error);
          setError(message.error || 'Failed to sign in with Google');
        }
        setLoading(false);
      }
    };

    // Initialize auth state
    initializeAuth();

    // Set up listeners
    chrome.storage.local.onChanged.addListener(handleStorageChange);
    browser.runtime.onMessage.addListener(handleMessage);

    // Cleanup function
    return () => {
      mounted = false;
      chrome.storage.local.onChanged.removeListener(handleStorageChange);
      // Note: browser.runtime.onMessage doesn't have removeListener in types
      // but it's safe to keep this for future compatibility
      (browser.runtime.onMessage as any).removeListener?.(handleMessage);
    };
  }, []); // Empty dependency array since we want this to run once on mount

  const handleLogin = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if running in extension context
      if (!window.chrome?.identity) {
        throw new Error('Chrome identity API not available. Make sure you are running in a Chrome extension.');
      }

      console.log('Starting Google sign-in process');
      // Send message to background service worker to handle login and wait for response
      const response = await browser.runtime.sendMessage({ type: 'START_GOOGLE_LOGIN' });

      // Handle the response
      if (response && response.type === 'GOOGLE_LOGIN_RESULT') {
        if (response.success) {
          console.log('Login successful:', response.authState);
          setAuthState(response.authState);
          setSuccess('Successfully signed in!');
        } else {
          console.error('Login failed:', response.error);
          setError(response.error || 'Failed to sign in with Google');
        }
      }

    } catch (error: any) {
      console.error('Login error details:', error);
      setError(error.message || 'Failed to sign in with Google');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Starting sign-out process');
      await handleSignOut();
      // Note: We don't need to set state here because the storage change listener will handle it
      console.log('Sign-out completed');
    } catch (error: any) {
      console.error('Logout error details:', error);
      setError('Failed to sign out. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handler for closing the popup (window.close for now)
  const handleClose = () => {
    window.close();
  };

  // Handler for opening settings
  const handleSettings = () => {
    setShowSettings(true);
  };

  // Handler for closing settings
  const handleCloseSettings = () => {
    setShowSettings(false);
  };

  // Handler to set prompt from Library/Community
  const handleSetPrompt = (value: string) => {
    setActiveTab(0);
    setPromptPanelValue(value);
  };
  // Handler to save to library from Community
  const handleSaveToLibrary = async (title: string, content: string) => {
    await addPromptLibrary({ title, content, tags: [], folder: '', optimize: '' });
  };

  if (loading) {
    return (
      <Box sx={{
        width: '350px',
        padding: '16px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '200px'
      }}>
        <CircularProgress />
      </Box>
    );
  }

  // Show login screen if not authenticated and showLoginScreen is true
  if (!authState.isAuthenticated && showLoginScreen) {
    return (
      <Box sx={{
        width: '100vw',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: '#f5f5f5'
      }}>
        <LoginWithGoogle
          onLogin={handleLogin}
          onClose={() => setShowLoginScreen(false)}
          loading={loading}
        />
      </Box>
    );
  }

  // Show settings panel if settings is open
  if (showSettings) {
    return <SettingsPanel onClose={handleCloseSettings} />;
  }

  return (
    <Box sx={{
      width: '400px',
      minHeight: '600px',
      display: 'flex',
      flexDirection: 'column',
      background: '#fafafa',
      fontFamily: 'Arial, sans-serif',
      boxShadow: 2,
      borderRadius: 2,
      overflow: 'hidden',
      position: 'relative'
    }}>
      <TopBar onSettings={handleSettings} onClose={handleClose} user={authState.isAuthenticated ? authState.userInfo : null} onLogout={handleLogout} />
      {error && (
        <Alert
          severity="error"
          onClose={() => setError(null)}
          sx={{ mb: 2 }}
        >
          {error}
        </Alert>
      )}
      <Snackbar
        open={!!success}
        autoHideDuration={3000}
        onClose={() => setSuccess(null)}
        message={success}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      />
      {/* Tab Bar */}
      <Tabs
        value={activeTab}
        onChange={(_, v) => setActiveTab(v)}
        variant="fullWidth"
        sx={{ borderBottom: '1px solid #e0e0e0', background: '#fff' }}
        aria-label="PromptPilot main tabs"
      >
        <Tab icon={<ChatIcon />} label="Optimize Prompt" />
        <Tab icon={<ForumIcon />} label="Saved Responses" />
        <Tab icon={<LibraryBooksIcon />} label="Prompt Library" />
        <Tab icon={<GroupIcon />} label="Community" />
      </Tabs>
      {/* Main Content */}
      <Box sx={{ flex: 1, overflow: 'auto', background: '#fafafa' }}>
        <TabPanel value={activeTab} index={0}>
          <PromptPanel key={activeTab} initialPrompt={promptPanelValue} setPromptPanelValue={setPromptPanelValue} />
        </TabPanel>
        <TabPanel value={activeTab} index={1}>
          <SavedResponses />
        </TabPanel>
        <TabPanel value={activeTab} index={2}>
          <LibraryPanel onUsePrompt={handleSetPrompt} />
        </TabPanel>
        <TabPanel value={activeTab} index={3}>
          <CommunityPanel onUsePrompt={handleSetPrompt} onSaveToLibrary={handleSaveToLibrary} />
        </TabPanel>
      </Box>
    </Box>
  );
}
