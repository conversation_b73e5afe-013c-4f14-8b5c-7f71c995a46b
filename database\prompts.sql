-- Table for storing user prompts
CREATE TABLE IF NOT EXISTS prompts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  tags TEXT[],
  folder VARCHAR(255),
  is_favorite BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for fast lookup by user
CREATE INDEX IF NOT EXISTS idx_prompts_user_id ON prompts(user_id);

-- Enable Row Level Security
ALTER TABLE prompts ENABLE ROW LEVEL SECURITY;

-- Create policies that work with custom authentication
-- These policies check that the user_id exists in the users table

-- Allow reading prompts (user_id must exist in users table)
CREATE POLICY "Enable read access for valid users" ON prompts
  FOR SELECT USING (
    user_id IN (SELECT id FROM users)
  );

-- Allow inserting prompts (user_id must exist in users table)
CREATE POLICY "Enable insert for valid users" ON prompts
  FOR INSERT WITH CHECK (
    user_id IN (SELECT id FROM users)
  );

-- Allow updating prompts (user_id must exist in users table)
CREATE POLICY "Enable update for valid users" ON prompts
  FOR UPDATE USING (
    user_id IN (SELECT id FROM users)
  );

-- Allow deleting prompts (user_id must exist in users table)
CREATE POLICY "Enable delete for valid users" ON prompts
  FOR DELETE USING (
    user_id IN (SELECT id FROM users)
  );

-- Auto-update updated_at timestamp
CREATE OR REPLACE FUNCTION update_prompts_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_prompts_updated_at ON prompts;
CREATE TRIGGER update_prompts_updated_at
  BEFORE UPDATE ON prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_prompts_updated_at_column(); 