# PromptPilot Extension

A Chrome extension for optimizing and managing AI prompts with Google authentication and Supabase database integration.

## Features

- 🔐 Google OAuth authentication
- 💾 User data storage in Supabase database
- 📝 Prompt optimization and management
- 📚 Personal prompt library
- 👥 Community prompt sharing
- ⚙️ Customizable settings

## Database Integration

This extension uses Supabase for user data storage. Users are automatically stored in the database when they log in with Google.

### Setup

1. **Create a Supabase project** at https://supabase.com
2. **Set environment variables** in your `.env` file:
   ```
   WXT_SUPABASE_URL=your-supabase-url
   WXT_SUPABASE_ANON_KEY=your-supabase-anon-key
   ```
3. **Create the database table** using the SQL in `database/users.sql`
4. **Follow the complete setup guide** in `DATABASE_SETUP.md`

### Database Features

- ✅ Unique user identification by email
- ✅ Google ID tracking
- ✅ Automatic timestamp management
- ✅ Row Level Security (RLS)
- ✅ Optimized database indexes

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## File Structure

- `utils/supabase.ts` - Supabase client configuration
- `utils/userService.ts` - User database operations
- `utils/auth.ts` - Authentication with database integration
- `database/users.sql` - Database schema and setup
- `DATABASE_SETUP.md` - Complete setup instructions
