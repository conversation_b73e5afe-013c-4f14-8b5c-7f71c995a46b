# Row Level Security (RLS) Troubleshooting Guide

## 🔍 The Problem

You were getting this error when trying to insert prompts:
```
{"code":"42501","details":null,"hint":null,"message":"new row violates row-level security policy for table \"prompts\""}
```

## 🔧 Root Cause

The issue was with the RLS policies in your `prompts` table:

1. **Authentication Mismatch**: Your Chrome extension uses custom authentication (not Supabase Auth)
2. **Policy Issue**: The original policies used `auth.email()` which only works with Supabase Auth users
3. **Missing Context**: Since your users aren't in Supabase's `auth.users` table, `auth.email()` returns `null`

## ✅ The Solution

### Step 1: Update RLS Policies

Run the SQL script `database/fix_rls_policies.sql` in your Supabase SQL editor:

```sql
-- Drop old policies that use auth.email()
DROP POLICY IF EXISTS "Enable read access for prompt owners" ON prompts;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON prompts;
DROP POLICY IF EXISTS "Enable update for prompt owners only" ON prompts;
DROP POLICY IF EXISTS "Enable delete for prompt owners only" ON prompts;

-- Create new policies that work with custom authentication
CREATE POLICY "Enable read access for valid users" ON prompts
  FOR SELECT USING (user_id IN (SELECT id FROM users));

CREATE POLICY "Enable insert for valid users" ON prompts
  FOR INSERT WITH CHECK (user_id IN (SELECT id FROM users));

CREATE POLICY "Enable update for valid users" ON prompts
  FOR UPDATE USING (user_id IN (SELECT id FROM users));

CREATE POLICY "Enable delete for valid users" ON prompts
  FOR DELETE USING (user_id IN (SELECT id FROM users));
```

### Step 2: Verify User Exists

Make sure the user exists in your `users` table before trying to insert prompts. The updated code now includes better error handling and logging.

## 🧪 Testing Steps

### 1. Check User Creation
```javascript
// In browser console, check if user exists
const auth = await getAuthState();
console.log('Auth state:', auth);

if (auth.isAuthenticated) {
  const userResult = await supabase
    .from('users')
    .select('*')
    .eq('email', auth.userInfo.email)
    .single();
  console.log('User in database:', userResult);
}
```

### 2. Test Prompt Insertion
```javascript
// Try inserting a test prompt
const testPrompt = {
  title: "Test Prompt",
  content: "This is a test prompt",
  tags: ["test"],
  folder: "Test Folder"
};

try {
  await addPromptLibrary(testPrompt);
  console.log('Prompt inserted successfully');
} catch (error) {
  console.error('Failed to insert prompt:', error);
}
```

### 3. Check RLS Policies
Run this in Supabase SQL editor to verify policies:
```sql
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'prompts';
```

## 🔍 Debugging Tips

### Check Browser Console
The updated code includes detailed logging:
- User authentication status
- User lookup results
- Prompt insertion data
- Error messages

### Common Issues

1. **User Not Found**: Make sure the user is created in the `users` table during authentication
2. **Invalid user_id**: Ensure the `user_id` being inserted exists in the `users` table
3. **RLS Still Blocking**: Verify the new policies are applied correctly

### Verify Database State

1. **Check if user exists**:
   ```sql
   SELECT * FROM users WHERE email = '<EMAIL>';
   ```

2. **Check prompts table structure**:
   ```sql
   \d prompts
   ```

3. **Check current policies**:
   ```sql
   SELECT * FROM pg_policies WHERE tablename = 'prompts';
   ```

## 🚀 Expected Behavior After Fix

1. ✅ Users can insert prompts after successful authentication
2. ✅ Users can read their own prompts
3. ✅ Users can update their own prompts
4. ✅ Users can delete their own prompts
5. ✅ Detailed logging helps with troubleshooting

## 📝 Policy Explanation

### Old Policies (Problematic)
```sql
-- This doesn't work with custom auth
FOR SELECT USING (user_id = (
  SELECT id FROM users WHERE email = auth.email()
));
```

### New Policies (Working)
```sql
-- This works with custom auth
FOR SELECT USING (
  user_id IN (SELECT id FROM users)
);
```

The new policies simply check that the `user_id` exists in the `users` table, which works with your custom authentication system.

## 🔄 Next Steps

1. **Run the SQL script** to update RLS policies
2. **Rebuild and test** the extension
3. **Check browser console** for detailed logs
4. **Verify** that prompts can be inserted and retrieved
5. **Monitor** for any remaining RLS issues

If you still encounter issues after applying these fixes, check the browser console for the detailed logging output to identify the specific problem.
