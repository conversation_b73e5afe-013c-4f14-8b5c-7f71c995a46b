import { useState, useMemo } from 'react';
import {
  <PERSON>, <PERSON>po<PERSON>, Snackbar, TextField, Select, MenuItem, InputLabel, FormControl, Chip, Card, CardContent, IconButton, Tooltip, Modal, Divider
} from '@mui/material';
import PromptSaveModal, { PromptData } from './PromptSaveModal';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import SaveIcon from '@mui/icons-material/Save';
import RocketLaunchIcon from '@mui/icons-material/RocketLaunch';
import StarIcon from '@mui/icons-material/Star';
import LabelIcon from '@mui/icons-material/Label';
import SmartToyIcon from '@mui/icons-material/SmartToy';

// Your original mockPrompts, now with 'author' field
const mockPrompts = [
  {
    id: '1',
    title: 'Blog writing',
    content: 'I’m looking for a [type of blog post] that will showcase the value and benefits of my [product/service] to [ideal customer persona] and convince them to take [desired action] with social proof and credibility-building elements.[PROMPT].[TARGETLANGUAGE].',
    tags: ['Copywriting', 'Blog'],
    model: 'ChatGPT',
    upvotes: 12,
    comments: 2,
    date: '2024-06-10',
    featured: false,
    author: 'Alice',
  },
  {
    id: '2',
    title: 'UX/UI design',
    content: 'I want you to act as a UX/UI developer. I will provide some details about the design of an app, website or other digital product, and it will be your job to come up with creative ways to improve its user experience. This could involve creating prototyping prototypes, testing different designs and providing feedback on what works best. My first request is [PROMPT].[TARGETLANGUAGE]',
    tags: ['Coding', 'UI/UX'],
    model: 'Claude',
    upvotes: 8,
    comments: 1,
    date: '2024-06-11',
    featured: false,
    author: 'Bob',
  },
  {
    id: '3',
    title: 'Facebook ads',
    content: 'I’m looking for a Facebook ad copy that will provide a sneak peek of upcoming products or services and create a sense of anticipation and excitement for my [ideal customer persona] with a clear and compelling call-to-action.[PROMPT].[TARGETLANGUAGE].',
    tags: ['FB Ads', 'Content Creation'],
    model: 'Gemini',
    upvotes: 15,
    comments: 0,
    date: '2024-06-12',
    featured: false,
    author: 'Carol',
  },
  {
    id: '4',
    title: 'Instagram idea',
    content: 'I’m looking for an Instagram story idea that will engage my [ideal customer persona] with behind-the-scenes content and persuade them to take [desired action] with a sense of exclusivity and authenticity.[PROMPT].[TARGETLANGUAGE].',
    tags: ['Instagram', 'Content Creation'],
    model: 'ChatGPT',
    upvotes: 15,
    comments: 3,
    date: '2024-06-13',
    featured: true,
    author: 'Dave',
  },
  {
    id: '5',
    title: 'AI Marketing Specialist',
    content: 'I want you to act as an AI Marketing Specialist, an expert in utilizing artificial intelligence in marketing, specializing in AI-powered customer segmentation and predictive analytics. My first suggestion request is to explore AI applications for our marketing strategy.',
    tags: ['Marketing'],
    model: 'ChatGPT',
    upvotes: 23,
    comments: 4,
    date: '2024-06-14',
    featured: false,
    author: 'Eve',
  },
  {
    id: '6',
    title: 'Youtube Creator',
    content: 'I want you to act as a YouTube Creator, an expert in producing compelling videos on YouTube, specializing in building a YouTube channel and engaging with subscribers. My first suggestion request is to develop a YouTube content plan.',
    tags: ['YT Creator', 'Content Creation'],
    model: 'ChatGPT',
    upvotes: 27,
    comments: 5,
    date: '2024-06-15',
    featured: false,
    author: 'Frank',
  },
  {
    id: '7',
    title: 'YouTube Video Ideas: Going Viral and Persuading [Ideal Customer Persona]',
    content: 'I need a YouTube video idea that will both go viral and persuade my [ideal customer persona] to take [desired action] on my [website/product] with a strong call-to-action and compelling visuals.[PROMPT].[TARGETLANGUAGE].',
    tags: ['YT Creator', 'Content Creation'],
    model: 'Gemini',
    upvotes: 33,
    comments: 6,
    date: '2024-06-16',
    featured: true,
    author: 'Grace',
  },
  {
    id: '8',
    title: 'YouTube Video Ideas: Going Viral and Showcasing [Product/Service]',
    content: 'I’m looking for a YouTube video idea that will go viral and showcase my [product/service] to my [ideal customer persona] in a creative and entertaining way.[PROMPT].[TARGETLANGUAGE].',
    tags: ['YT Creator', 'Content Creation'],
    model: 'ChatGPT',
    upvotes: 25,
    comments: 2,
    date: '2024-06-17',
    featured: false,
    author: 'Heidi',
  },
  {
    id: '9',
    title: 'TikTok Creator',
    content: 'I want you to act as a TikTok Creator, an expert in creating engaging videos on TikTok, specializing in leveraging trends and building a TikTok following. My first suggestion request is to create a series of captivating TikTok videos for our brand.',
    tags: ['Tiktok', 'Content Creation'],
    model: 'ChatGPT',
    upvotes: 32,
    comments: 7,
    date: '2024-06-18',
    featured: false,
    author: 'Ivan',
  },
  {
    id: '10',
    title: 'StackOverflow Post',
    content: 'I want you to act as a stackoverflow post. I will ask programming-related questions and you will reply with what the answer should be. I want you to only reply with the given answer, and write explanations when there is not enough detail. do not write explanations. When I need to tell you something in English, I will do so by putting text inside curly brackets {like this}. My first question is [PROMPT].[TARGETLANGUAGE].',
    tags: ['StackOverflow'],
    model: 'ChatGPT',
    upvotes: 17,
    comments: 1,
    date: '2024-06-19',
    featured: false,
    author: 'Judy',
  },
];

const ALL_TAGS = Array.from(new Set(mockPrompts.flatMap(p => p.tags)));
const ALL_LLM = Array.from(new Set(mockPrompts.map(p => p.model)));
const SORT_OPTIONS = [
  { value: 'date', label: 'Date' },
  { value: 'upvotes', label: 'Upvotes' },
  { value: 'comments', label: 'Comments' },
  { value: 'featured', label: 'Featured' },
];
const FEATURED_OPTIONS = [
  { value: '', label: 'All' },
  { value: 'featured', label: 'Featured' },
  { value: 'not_featured', label: 'Not Featured' },
];

export default function CommunityPanel({ onUsePrompt, onSaveToLibrary }: { onUsePrompt: (prompt: string) => void; onSaveToLibrary: (title: string, content: string) => void }) {
  // Filter/search state
  const [search, setSearch] = useState('');
  const [selectedTag, setSelectedTag] = useState('');
  const [selectedLLM, setSelectedLLM] = useState('ChatGPT');
  const [sortBy, setSortBy] = useState('date');
  const [featured, setFeatured] = useState('');
  // Snackbar
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({ open: false, message: '', severity: 'success' });
  // Modals
  const [viewModal, setViewModal] = useState<{ open: boolean; prompt: any | null }>({ open: false, prompt: null });
  const [submitModalOpen, setSubmitModalOpen] = useState(false);

  // Filtered and sorted prompts
  const filteredPrompts = useMemo(() => {
    let prompts = mockPrompts.filter(p => {
      const matchesSearch =
        p.title.toLowerCase().includes(search.toLowerCase()) ||
        p.content.toLowerCase().includes(search.toLowerCase()) ||
        p.tags.some(tag => tag.toLowerCase().includes(search.toLowerCase()));
      const matchesTag = selectedTag ? p.tags.includes(selectedTag) : true;
      const matchesLLM = selectedLLM ? p.model === selectedLLM : true;
      const matchesFeatured =
        featured === 'featured' ? p.featured :
        featured === 'not_featured' ? !p.featured : true;
      return matchesSearch && matchesTag && matchesLLM && matchesFeatured;
    });
    // Sort
    if (sortBy === 'date') {
      prompts = prompts.slice().sort((a, b) => b.date.localeCompare(a.date));
    } else if (sortBy === 'upvotes') {
      prompts = prompts.slice().sort((a, b) => b.upvotes - a.upvotes);
    } else if (sortBy === 'comments') {
      prompts = prompts.slice().sort((a, b) => (b.comments || 0) - (a.comments || 0));
    } else if (sortBy === 'featured') {
      prompts = prompts.slice().sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0));
    }
    return prompts;
  }, [search, selectedTag, selectedLLM, sortBy, featured]);

  // Actions
  const handleUse = async (promptContent: string) => {
    if (!promptContent.trim()) {
      setSnackbar({ open: true, message: 'Prompt is empty.', severity: 'error' });
      return;
    }

    let url = '';
    let selector = '';

    switch (selectedLLM) {
      case 'ChatGPT':
        url = 'https://chat.openai.com/';
        selector = 'textarea[data-id="root"], textarea[placeholder*="Message"], textarea[placeholder*="Send a message"]';
        break;
      case 'Gemini':
        url = 'https://gemini.google.com/app';
        selector = 'textarea[placeholder*="Message"], textarea[aria-label*="Message"], .ql-editor, [contenteditable="true"]';
        break;
      case 'Claude':
        url = 'https://claude.ai/';
        selector = 'textarea[placeholder*="Message"], textarea[aria-label*="Message"], .ql-editor, [contenteditable="true"]';
        break;
      default:
        setSnackbar({ open: true, message: 'Invalid LLM selection.', severity: 'error' });
        return;
    }

    setSnackbar({ open: true, message: `Opening ${selectedLLM}...`, severity: 'success' });

    chrome.runtime.sendMessage({
      action: 'openAndFillPrompt',
      url,
      prompt: promptContent,
      selector
    }, (response) => {
      if (response && response.success) {
        setSnackbar({ open: true, message: `Prompt filled in ${selectedLLM}!`, severity: 'success' });
      } else {
        setSnackbar({ open: true, message: response && response.error ? response.error : 'Failed to fill prompt.', severity: 'error' });
      }
    });
  };
  const handleSave = (title: string, content: string) => {
    onSaveToLibrary(title, content);
    setSnackbar({ open: true, message: 'Prompt saved to Library!', severity: 'success' });
  };
  const handleCopy = (content: string) => {
    navigator.clipboard.writeText(content);
    setSnackbar({ open: true, message: 'Prompt copied!', severity: 'success' });
  };
  const handleView = (prompt: any) => {
    setViewModal({ open: true, prompt });
  };
  const handleCloseView = () => setViewModal({ open: false, prompt: null });

  // Submit modal
  const handleOpenSubmit = () => setSubmitModalOpen(true);
  const handleCloseSubmit = () => setSubmitModalOpen(false);
  const handleSubmitPrompt = (data: PromptData) => {
    setSubmitModalOpen(false);
    setSnackbar({ open: true, message: 'Prompt submitted!', severity: 'success' });
  };

  return (
    <Box sx={{ p: 0, display: 'flex', flexDirection: 'column', height: '100%', minHeight: 0 }}>
      {/* Search & Filters */}
      <Box sx={{ p: 2, pb: 1, borderBottom: '1px solid #e0e0e0', background: '#fff', zIndex: 1 }}>
        <Typography fontWeight={600} fontSize={15} sx={{ mb: 1 }}>🔍 Search & Filters</Typography>
        <TextField
          value={search}
          onChange={e => setSearch(e.target.value)}
          placeholder="Search prompts by keyword..."
          size="small"
          fullWidth
          InputProps={{
            startAdornment: <span role="img" aria-label="search">🔎</span>,
            style: { borderRadius: 8 }
          }}
          sx={{ mb: 1 }}
        />
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>🏷️ Tag</InputLabel>
            <Select
              value={selectedTag}
              label="🏷️ Tag"
              onChange={e => setSelectedTag(e.target.value)}
            >
              <MenuItem value="">All Tags</MenuItem>
              {ALL_TAGS.map(tag => (
                <MenuItem key={tag} value={tag}>{tag}</MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>🤖 LLM</InputLabel>
            <Select
              value={selectedLLM}
              label="🤖 LLM"
              onChange={e => setSelectedLLM(e.target.value)}
            >
              <MenuItem value="">All LLMs</MenuItem>
              {ALL_LLM.map(llm => (
                <MenuItem key={llm} value={llm}>{llm}</MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>📅 Sort</InputLabel>
            <Select
              value={sortBy}
              label="📅 Sort"
              onChange={e => setSortBy(e.target.value)}
            >
              {SORT_OPTIONS.map(opt => (
                <MenuItem key={opt.value} value={opt.value}>{opt.label}</MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 100, flex: 1 }}>
            <InputLabel>🏅 Featured</InputLabel>
            <Select
              value={featured}
              label="🏅 Featured"
              onChange={e => setFeatured(e.target.value)}
            >
              {FEATURED_OPTIONS.map(opt => (
                <MenuItem key={opt.value} value={opt.value}>{opt.label}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>

      {/* Prompt Cards List */}
      <Box sx={{ flex: 1, overflowY: 'auto', background: '#f9fafb', p: 2, display: 'flex', flexDirection: 'column', gap: 2, minHeight: 0 }}>
        {filteredPrompts.length === 0 ? (
          <Typography color="text.secondary" sx={{ mt: 4, textAlign: 'center' }}>No prompts found.</Typography>
        ) : (
          filteredPrompts.map(item => (
            <Card key={item.id} sx={{ borderRadius: 2, border: '1px solid #e0e0e0', boxShadow: 0, p: 0, position: 'relative', overflow: 'visible' }}>
              {item.featured && (
                <Box sx={{ position: 'absolute', top: 8, right: 8, bgcolor: '#fef08a', color: '#b45309', px: 1, py: 0.5, borderRadius: 1, fontSize: 11, fontWeight: 700, zIndex: 1 }}>
                  <StarIcon fontSize="inherit" sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} /> FEATURED
                </Box>
              )}
              <CardContent sx={{ p: 2, pb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <span role="img" aria-label="brain" style={{ fontSize: 20 }}>🧠</span>
                  <Typography fontWeight={700} fontSize={15} sx={{ flex: 1 }}>{item.title}</Typography>
                </Box>
                <Typography fontSize={13} color="text.secondary" sx={{ mb: 1, whiteSpace: 'pre-line' }}>{item.content.length > 120 ? item.content.slice(0, 120) + '...' : item.content}</Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center', mb: 1 }}>
                  {item.tags.map(tag => (
                    <Chip key={tag} icon={<LabelIcon sx={{ fontSize: 16 }} />} label={tag} size="small" sx={{ bgcolor: '#f3e8ff', color: '#7c3aed', fontWeight: 500, '& .MuiChip-icon': { color: '#a855f7' } }} />
                  ))}
                  <Chip icon={<SmartToyIcon sx={{ fontSize: 16 }} />} label={item.model} size="small" sx={{ bgcolor: '#dbeafe', color: '#1e40af', fontWeight: 500, '& .MuiChip-icon': { color: '#3b82f6' } }} />
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, fontSize: 12, color: '#64748b', mb: 1 }}>
                  <span>👍 {item.upvotes}</span>
                  <span>💬 {item.comments}</span>
                  <span>📅 {item.date}</span>
                  {item.featured && <span>🏅 Featured</span>}
                  <span style={{ marginLeft: 'auto', color: '#2563eb', fontWeight: 500 }}>👤 {item.author}</span>
                </Box>
                <Divider sx={{ my: 1 }} />
                <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'nowrap', justifyContent: 'flex-end' }}>
                  <Tooltip title="Use Prompt"><IconButton color="primary" size="small" onClick={() => handleUse(item.content)}><RocketLaunchIcon /></IconButton></Tooltip>
                  <Tooltip title="Save to Library"><IconButton size="small" onClick={() => handleSave(item.title, item.content)}><SaveIcon /></IconButton></Tooltip>
                  <Tooltip title="Copy"><IconButton size="small" onClick={() => handleCopy(item.content)}><ContentCopyIcon /></IconButton></Tooltip>
                  <Tooltip title="View Details"><IconButton size="small" onClick={() => handleView(item)}><VisibilityIcon /></IconButton></Tooltip>
                </Box>
              </CardContent>
            </Card>
          ))
        )}
      </Box>

      {/* Submit Your Prompt */}
      <Box sx={{ p: 2, borderTop: '1px solid #e0e0e0', background: '#fff' }}>
        <button style={{ width: '100%', fontWeight: 700, borderRadius: 8, padding: '10px 0', background: '#22c55e', color: '#fff', border: 'none', fontSize: 16, cursor: 'pointer' }} onClick={handleOpenSubmit}>
          ➕ Submit Your Prompt
        </button>
      </Box>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar(s => ({ ...s, open: false }))}
        message={snackbar.message}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        ContentProps={{ style: { background: snackbar.severity === 'error' ? '#d32f2f' : '#388e3c', color: '#fff' } }}
      />

      {/* View Modal */}
      <Modal open={viewModal.open} onClose={handleCloseView}>
        <Box sx={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', width: 380, bgcolor: '#fff', borderRadius: 2, boxShadow: 24, p: 3, maxHeight: '80vh', overflowY: 'auto' }}>
          {viewModal.prompt && (
            <>
              <Typography variant="h6" fontWeight={700} sx={{ mb: 1 }}>{viewModal.prompt.title}</Typography>
              <Typography fontSize={14} color="text.secondary" sx={{ mb: 2, whiteSpace: 'pre-line' }}>{viewModal.prompt.content}</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center', mb: 2 }}>
                {viewModal.prompt.tags.map((tag: string) => (
                  <Chip key={tag} icon={<LabelIcon sx={{ fontSize: 16 }} />} label={tag} size="small" sx={{ bgcolor: '#f3e8ff', color: '#7c3aed', fontWeight: 500, '& .MuiChip-icon': { color: '#a855f7' } }} />
                ))}
                <Chip icon={<SmartToyIcon sx={{ fontSize: 16 }} />} label={viewModal.prompt.model} size="small" sx={{ bgcolor: '#dbeafe', color: '#1e40af', fontWeight: 500, '& .MuiChip-icon': { color: '#3b82f6' } }} />
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, fontSize: 12, color: '#64748b', mb: 1 }}>
                <span>👍 {viewModal.prompt.upvotes}</span>
                <span>💬 {viewModal.prompt.comments}</span>
                <span>📅 {viewModal.prompt.date}</span>
                {viewModal.prompt.featured && <span>🏅 Featured</span>}
                <span style={{ marginLeft: 'auto', color: '#2563eb', fontWeight: 500 }}>👤 {viewModal.prompt.author}</span>
              </Box>
              <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'nowrap', justifyContent: 'flex-end', mt: 2 }}>
                <IconButton color="primary" size="small" onClick={() => handleUse(viewModal.prompt.content)}><RocketLaunchIcon /></IconButton>
                <IconButton size="small" onClick={() => handleSave(viewModal.prompt.title, viewModal.prompt.content)}><SaveIcon /></IconButton>
                <IconButton size="small" onClick={() => handleCopy(viewModal.prompt.content)}><ContentCopyIcon /></IconButton>
              </Box>
            </>
          )}
        </Box>
      </Modal>

      {/* Submit Modal */}
      <PromptSaveModal
        open={submitModalOpen}
        onClose={handleCloseSubmit}
        onSave={handleSubmitPrompt}
        allFolders={[]}
        mode="add"
      />
    </Box>
  );
} 