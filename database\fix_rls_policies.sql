-- Fix RLS policies for Chrome extension compatibility
-- Run this script in your Supabase SQL editor

-- First, drop existing policies that use auth.email() (which doesn't work with custom auth)
DROP POLICY IF EXISTS "Enable read access for prompt owners" ON prompts;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON prompts;
DROP POLICY IF EXISTS "Enable update for prompt owners only" ON prompts;
DROP POLICY IF EXISTS "Enable delete for prompt owners only" ON prompts;

-- Create new policies that work with custom authentication
-- These policies check that the user_id exists in the users table

-- Allow reading prompts (user_id must exist in users table)
CREATE POLICY "Enable read access for valid users" ON prompts
  FOR SELECT USING (
    user_id IN (SELECT id FROM users)
  );

-- Allow inserting prompts (user_id must exist in users table)
CREATE POLICY "Enable insert for valid users" ON prompts
  FOR INSERT WITH CHECK (
    user_id IN (SELECT id FROM users)
  );

-- Allow updating prompts (user_id must exist in users table)
CREATE POLICY "Enable update for valid users" ON prompts
  FOR UPDATE USING (
    user_id IN (SELECT id FROM users)
  );

-- Allow deleting prompts (user_id must exist in users table)
CREATE POLICY "Enable delete for valid users" ON prompts
  FOR DELETE USING (
    user_id IN (SELECT id FROM users)
  );

-- Verify the policies are created correctly
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'prompts';
